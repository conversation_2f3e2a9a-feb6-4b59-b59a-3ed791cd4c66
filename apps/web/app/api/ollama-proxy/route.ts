import { log } from "@repo/shared/logger";
import { NextResponse, type NextRequest } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // Get user's Ollama URL from headers or use default
        const ollamaUrl = request.headers.get("x-ollama-url") || "http://127.0.0.1:11434";

        // Security: Only allow localhost URLs
        const url = new URL(ollamaUrl);
        const LOCAL_HOSTS = new Set(["localhost", "127.0.0.1", "::1", "0.0.0.0"]);

        if (!LOCAL_HOSTS.has(url.hostname)) {
            return NextResponse.json(
                { error: "Only localhost URLs are allowed for security" },
                { status: 400 },
            );
        }

        // Ensure the URL has the /v1 endpoint
        const baseURL = ollamaUrl.includes("/v1")
            ? ollamaUrl
            : `${ollamaUrl.replace(/\/+$/, "")}/v1`;

        // Forward request to local Ollama
        const response = await fetch(`${baseURL}/chat/completions`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "User-Agent": "VTChat-Ollama-Proxy/1.0",
            },
            body: JSON.stringify(body),
            // Timeout after 60 seconds
            signal: AbortSignal.timeout(60000),
        });

        if (!response.ok) {
            const errorText = await response.text();
            log.error("Ollama proxy error:", {
                status: response.status,
                statusText: response.statusText,
                error: errorText,
                url: baseURL,
            });

            return NextResponse.json(
                {
                    error: "Ollama server error",
                    details: `Status ${response.status}: ${response.statusText}`,
                    suggestion: "Make sure Ollama is running with: ollama serve",
                    url: baseURL,
                },
                { status: response.status },
            );
        }

        const data = await response.json();
        return NextResponse.json(data);
    } catch (error: any) {
        log.error("Ollama proxy request failed:", { error });

        // Provide helpful error messages
        if (error.name === "AbortError") {
            return NextResponse.json(
                {
                    error: "Request timeout",
                    suggestion:
                        "The local model may be slow. Try a smaller model or increase timeout.",
                },
                { status: 408 },
            );
        }

        if (error.code === "ECONNREFUSED") {
            return NextResponse.json(
                {
                    error: "Cannot connect to Ollama",
                    suggestion: "Start Ollama server with: ollama serve",
                    troubleshooting: [
                        "Verify Ollama is installed and running",
                        "Check that the server is started",
                        "Ensure no firewall is blocking port 11434",
                        "Try restarting Ollama service",
                    ],
                },
                { status: 503 },
            );
        }

        return NextResponse.json(
            {
                error: "Local AI connection failed",
                suggestion: "Check that Ollama is running and accessible",
                details: error.message,
            },
            { status: 500 },
        );
    }
}

export async function GET() {
    return NextResponse.json({
        message: "Ollama proxy endpoint",
        usage: "POST to this endpoint to proxy requests to your local Ollama instance",
        headers: {
            "x-ollama-url": "Optional custom Ollama URL (default: http://127.0.0.1:11434)",
        },
    });
}
