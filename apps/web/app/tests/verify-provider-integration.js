// Comprehensive test script for verifying provider integrations

import { createAnthropic } from "@ai-sdk/anthropic";
import { createOpenAI } from "@ai-sdk/openai";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";

// Test configuration
const CONFIG = {
    // Set to true to test each provider
    testOpenAI: false,
    testAnthropic: false,
    testOpenRouter: false,
    testLMStudio: true,
    testOllama: true,

    // API Keys (for local testing only, never commit real keys)
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || "",
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY || "",
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || "",
    LMSTUDIO_BASE_URL: process.env.LMSTUDIO_BASE_URL || "http://localhost:1234",
    OLLAMA_BASE_URL: process.env.OLLAMA_BASE_URL || "http://127.0.0.1:11434",
};

// Test prompt
const TEST_PROMPT =
    "Hello! This is a test of the provider integration. Please respond with a short greeting.";

async function testProvider(name, testFn) {
    console.log(`\n🧪 Testing ${name} provider...`);
    try {
        const result = await testFn();
        console.log(`✅ ${name} test passed!`);
        console.log("Response:", result);
        return { success: true, provider: name, response: result };
    } catch (error) {
        console.error(`❌ ${name} test failed:`, error.message);
        return { success: false, provider: name, error: error.message };
    }
}

async function testOpenAI() {
    if (!CONFIG.OPENAI_API_KEY) {
        throw new Error("OpenAI API key not provided");
    }

    const openai = createOpenAI({
        apiKey: CONFIG.OPENAI_API_KEY,
    });

    const { text } = await generateText({
        model: openai("gpt-3.5-turbo"),
        prompt: TEST_PROMPT,
        maxTokens: 100,
    });

    return text;
}

async function testAnthropic() {
    if (!CONFIG.ANTHROPIC_API_KEY) {
        throw new Error("Anthropic API key not provided");
    }

    const anthropic = createAnthropic({
        apiKey: CONFIG.ANTHROPIC_API_KEY,
    });

    const { text } = await generateText({
        model: anthropic("claude-3-haiku-20240307"),
        prompt: TEST_PROMPT,
        maxTokens: 100,
    });

    return text;
}

async function testOpenRouter() {
    if (!CONFIG.OPENROUTER_API_KEY) {
        throw new Error("OpenRouter API key not provided");
    }

    const openrouter = createOpenRouter({
        apiKey: CONFIG.OPENROUTER_API_KEY,
    });

    const { text } = await generateText({
        model: openrouter("openai/gpt-3.5-turbo"),
        prompt: TEST_PROMPT,
        maxTokens: 100,
    });

    return text;
}

async function testLMStudio() {
    console.log(`📡 Connecting to LM Studio at: ${CONFIG.LMSTUDIO_BASE_URL}`);

    const lmstudio = createOpenAICompatible({
        name: "lmstudio",
        baseURL: `${CONFIG.LMSTUDIO_BASE_URL}/v1`,
    });

    const { text } = await generateText({
        model: lmstudio("llama-3-8b-instruct"),
        prompt: TEST_PROMPT,
        maxRetries: 1,
        temperature: 0.7,
        maxTokens: 100,
    });

    return text;
}

async function testOllama() {
    console.log(`📡 Connecting to Ollama at: ${CONFIG.OLLAMA_BASE_URL}`);

    const ollama = createOpenAICompatible({
        name: "ollama",
        baseURL: `${CONFIG.OLLAMA_BASE_URL}/api/v1`,
    });

    const { text } = await generateText({
        model: ollama("llama3"),
        prompt: TEST_PROMPT,
        maxRetries: 1,
        temperature: 0.7,
        maxTokens: 100,
    });

    return text;
}

async function runTests() {
    console.log("🚀 Starting provider integration tests...");

    const results = [];

    if (CONFIG.testOpenAI) {
        results.push(await testProvider("OpenAI", testOpenAI));
    }

    if (CONFIG.testAnthropic) {
        results.push(await testProvider("Anthropic", testAnthropic));
    }

    if (CONFIG.testOpenRouter) {
        results.push(await testProvider("OpenRouter", testOpenRouter));
    }

    if (CONFIG.testLMStudio) {
        results.push(await testProvider("LM Studio", testLMStudio));
    }

    if (CONFIG.testOllama) {
        results.push(await testProvider("Ollama", testOllama));
    }

    // Print summary
    console.log("\n📊 Test Summary:");
    const passed = results.filter((r) => r.success).length;
    const failed = results.filter((r) => !r.success).length;
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);

    if (failed > 0) {
        console.log("\n❌ Failed tests:");
        results
            .filter((r) => !r.success)
            .forEach((result) => {
                console.log(`- ${result.provider}: ${result.error}`);
            });

        console.log("\n🔧 Troubleshooting tips:");
        console.log("1. Check that all required API keys are provided");
        console.log("2. For LM Studio: Make sure the server is running and a model is loaded");
        console.log("3. For Ollama: Make sure the service is running and models are pulled");
        console.log("4. Check network connectivity and firewall settings");
    }

    return {
        success: failed === 0,
        passed,
        failed,
        results,
    };
}

// Run the tests if this file is executed directly
if (require.main === module) {
    runTests()
        .then((summary) => {
            if (summary.success) {
                console.log("\n🎉 All tests passed!");
                process.exit(0);
            } else {
                console.error("\n❌ Some tests failed. See summary above.");
                process.exit(1);
            }
        })
        .catch((error) => {
            console.error("❌ Test execution failed:", error);
            process.exit(1);
        });
}

export default runTests;
