@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global scrollbar styling */
html,
body {
    /* Firefox - completely hide scrollbars globally */
    scrollbar-width: none;
    scrollbar-color: transparent transparent;
}

/* WebKit browsers - hide scrollbars globally */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    display: none;
}

html::-webkit-scrollbar-track,
body::-webkit-scrollbar-track {
    background: transparent;
    display: none;
}

html::-webkit-scrollbar-thumb,
body::-webkit-scrollbar-thumb {
    background: transparent;
    display: none;
}

html::-webkit-scrollbar-corner,
body::-webkit-scrollbar-corner {
    background: transparent;
    display: none;
}

/* Firefox specific - force hide all scrollbars */
@-moz-document url-prefix() {
    html,
    body {
        scrollbar-width: none !important;
        scrollbar-color: transparent transparent !important;
    }

    * {
        scrollbar-width: none !important;
        scrollbar-color: transparent transparent !important;
    }

    /* Override any default scrollbar styling */
    ::-moz-scrollbar {
        width: 0 !important;
        height: 0 !important;
        display: none !important;
    }

    ::-moz-scrollbar-track {
        display: none !important;
    }

    ::-moz-scrollbar-thumb {
        display: none !important;
    }
}

/* Mobile-specific styles for RAG chat */
@media (max-width: 768px) {
    .mobile-chat-container {
        height: 100vh;
        height: 100dvh; /* Use dynamic viewport height on supported browsers */
        position: relative;
        overflow: hidden;
    }

    .mobile-chat-scroll {
        height: calc(100vh - 140px); /* Account for header and input */
        height: calc(100dvh - 140px);
        min-height: 300px;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
    }

    .mobile-message-bubble {
        max-width: 90%;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Improve touch targets on mobile */
    .mobile-button {
        min-height: 44px;
        min-width: 44px;
    }

    /* Safe area handling for iOS */
    .mobile-safe-top {
        padding-top: env(safe-area-inset-top);
    }

    .mobile-safe-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }
}

@keyframes fade458 {
    from {
        opacity: 1;
    }

    to {
        opacity: 0.25;
    }
}

@layer base {
    :root {
        /* Vaul (drawer) overlay variables */
        --vaul-overlay-background: 255, 255, 255;
        --vaul-overlay-background-start: transparent;
        --vaul-overlay-background-end: rgba(0, 0, 0, 0.4);

        /* Official Shadcn UI theming variables - Stone theme */
        --radius: 0.625rem;
        --background: 0 0% 100%;
        --foreground: 20 14.3% 4.1%;
        --card: 0 0% 100%;
        --card-foreground: 20 14.3% 4.1%;
        --popover: 0 0% 100%;
        --popover-foreground: 20 14.3% 4.1%;
        --primary: 24 9.8% 10%;
        --primary-foreground: 60 9.1% 97.8%;
        --secondary: 60 4.8% 95.9%;
        --secondary-foreground: 24 9.8% 10%;
        --muted: 60 4.8% 95.9%;
        --muted-foreground: 25 5.3% 34.7%;
        --accent: 60 4.8% 95.9%;
        --accent-foreground: 24 9.8% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 60 9.1% 97.8%;
        --border: 20 5.9% 90%;
        --input: 20 5.9% 90%;
        --ring: 20 14.3% 4.1%;
        --chart-1: 332 59% 55%;
        --chart-2: 236 32% 34%;
        --chart-3: 220 23% 13%;
        --chart-4: 44 28% 66%;
        --chart-5: 0 53% 42%;
        --sidebar: 60 9.1% 97.8%;
        --sidebar-foreground: 20 14.3% 4.1%;
        --sidebar-primary: 24 9.8% 10%;
        --sidebar-primary-foreground: 60 9.1% 97.8%;
        --sidebar-accent: 60 4.8% 95.9%;
        --sidebar-accent-foreground: 24 9.8% 10%;
        --sidebar-border: 20 5.9% 90%;
        --sidebar-ring: 20 14.3% 4.1%;

        /* Custom project variables (mapped to Shadcn variables for compatibility) */
        --brand: 24 9.8% 10%;
        --brand-foreground: 60 9.1% 97.8%;
        --tertiary: var(--muted);
        --tertiary-foreground: var(--muted-foreground);
        --quaternary: var(--accent);
        --quaternary-foreground: var(--accent-foreground);
        --soft: var(--border);
        --hard: var(--input);
        --border-soft: var(--border);
        --border-hard: var(--input);

        /* Custom shadow variables */
        --shadow-subtle-xs:
            rgba(0, 0, 0, 0.04) 0px 1px 1px -0.5px, rgba(0, 0, 0, 0.08) 0px 2px 2px -1px,
            rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
        --shadow-subtle-sm:
            rgba(0, 0, 0, 0.03) 0px 1px 1px -0.5px, rgba(0, 0, 0, 0.06) 0px 1px 1px -0.5px,
            rgba(0, 0, 0, 0.06) 0px 2px 2px -1px, rgba(0, 0, 0, 0.05) 0px 0px 0px 0.5px;
    }

    .dark {
        --background: 0 0% 3%;
        --foreground: 60 9.1% 97.8%;
        --card: 0 0% 5%;
        --card-foreground: 60 9.1% 97.8%;
        --popover: 0 0% 5%;
        --popover-foreground: 60 9.1% 97.8%;
        --primary: 20 5.9% 90%;
        --primary-foreground: 0 0% 3%;
        --secondary: 0 0% 8%;
        --secondary-foreground: 60 9.1% 97.8%;
        --muted: 0 0% 8%;
        --muted-foreground: 24 5.4% 73.9%;
        --accent: 0 0% 8%;
        --accent-foreground: 60 9.1% 97.8%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 60 9.1% 97.8%;
        --border: 0 0% 12%;
        --input: 0 0% 12%;
        --ring: 24 5.4% 63.9%;
        --chart-1: 332 59% 55%;
        --chart-2: 236 32% 34%;
        --chart-3: 220 23% 13%;
        --chart-4: 44 28% 66%;
        --chart-5: 0 53% 42%;
        --sidebar: 0 0% 2%;
        --sidebar-foreground: 60 9.1% 97.8%;
        --sidebar-primary: 220 70% 50%;
        --sidebar-primary-foreground: 60 9.1% 97.8%;
        --sidebar-accent: 0 0% 6%;
        --sidebar-accent-foreground: 60 9.1% 97.8%;
        --sidebar-border: 0 0% 10%;
        --sidebar-ring: 24 5.4% 63.9%;

        /* Custom project variables (mapped to Shadcn variables for compatibility) */
        --brand: 20 5.9% 90%;
        --brand-foreground: 24 9.8% 10%;
        --tertiary: var(--muted);
        --tertiary-foreground: var(--muted-foreground);
        --quaternary: var(--accent);
        --quaternary-foreground: var(--accent-foreground);
        --soft: var(--border);
        --hard: var(--input);
        --border-soft: var(--border);
        --border-hard: var(--input);

        /* Custom shadow variables for dark mode */
        --shadow-subtle-xs:
            rgba(0, 0, 0, 0.2) 0px 1px 1px -0.5px, rgba(0, 0, 0, 0.3) 0px 2px 2px -1px,
            rgba(0, 0, 0, 0.3) 0px 0px 0px 1px;
        --shadow-subtle-sm:
            rgba(0, 0, 0, 0.15) 0px 1px 1px -0.5px, rgba(0, 0, 0, 0.25) 0px 1px 1px -0.5px,
            rgba(0, 0, 0, 0.25) 0px 2px 2px -1px, rgba(0, 0, 0, 0.2) 0px 0px 0px 0.5px;
    }

    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
        font-family:
            "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
            Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    }

    strong {
        @apply font-medium;
    }
}

html.dark .shiki,
html.dark .shiki span {
    color: var(--shiki-dark) !important;
    background-color: var(--background) !important;
    /* Optional, if you also want font styles */
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
}

@layer utilities {
    /* Safe area support for mobile devices */
    .pb-safe {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .pt-safe {
        padding-top: env(safe-area-inset-top);
    }

    .pl-safe {
        padding-left: env(safe-area-inset-left);
    }

    .pr-safe {
        padding-right: env(safe-area-inset-right);
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        width: 0px !important;
        height: 0px !important;
        display: none !important;
    }

    .no-scrollbar::-webkit-scrollbar-track {
        display: none !important;
    }

    .no-scrollbar::-webkit-scrollbar-thumb {
        display: none !important;
    }

    .no-scrollbar::-webkit-scrollbar-corner {
        display: none !important;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none !important;
        /* IE and Edge */
        scrollbar-width: none !important;
        scrollbar-color: transparent transparent !important;
        /* Firefox */
    }

    /* Custom scrollbar styling */
    .custom-scrollbar {
        /* Firefox */
        scrollbar-width: thin;
        scrollbar-color: hsl(var(--border)) transparent;
    }

    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: hsl(var(--border));
        border-radius: 3px;
        transition: background-color 0.2s ease;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--muted-foreground) / 0.5);
    }

    .custom-scrollbar::-webkit-scrollbar-corner {
        background: transparent;
    }

    /* Thin scrollbar variant */
    .thin-scrollbar {
        /* Firefox */
        scrollbar-width: thin;
        scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
    }

    .thin-scrollbar::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    .thin-scrollbar::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 2px;
    }

    .thin-scrollbar::-webkit-scrollbar-thumb {
        background: hsl(var(--muted-foreground) / 0.3);
        border-radius: 2px;
        transition: background-color 0.2s ease;
    }

    .thin-scrollbar::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--muted-foreground) / 0.6);
    }

    .thin-scrollbar::-webkit-scrollbar-corner {
        background: transparent;
    }

    /* Premium Glass Morphism Effect */
    .glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .glass-dark {
        background: rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* VT+ Premium Glass Badge */
    .vt-plus-glass {
        background: linear-gradient(
            135deg,
            rgba(217, 154, 78, 0.15) 0%,
            rgba(0, 0, 0, 0.8) 50%,
            rgba(217, 154, 78, 0.1) 100%
        );
        backdrop-filter: blur(12px);
        border: 1px solid rgba(217, 154, 78, 0.3);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            0 2px 8px rgba(217, 154, 78, 0.2),
            inset 0 1px 1px rgba(255, 255, 255, 0.1);
        position: relative;
        overflow: hidden;
    }

    /* Light theme adjustments */
    .light .vt-plus-glass {
        background: linear-gradient(
            135deg,
            rgba(217, 154, 78, 0.25) 0%,
            rgba(255, 255, 255, 0.9) 50%,
            rgba(217, 154, 78, 0.2) 100%
        );
        backdrop-filter: blur(12px);
        border: 1px solid rgba(217, 154, 78, 0.4);
        box-shadow:
            0 8px 32px rgba(217, 154, 78, 0.15),
            0 2px 8px rgba(217, 154, 78, 0.25),
            inset 0 1px 1px rgba(255, 255, 255, 0.8);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Light theme pseudo-element adjustments */
    .light .vt-plus-glass::before {
        background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(217, 154, 78, 0.15) 50%,
            transparent 100%
        );
    }

    .vt-plus-glass::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(217, 154, 78, 0.2) 50%,
            transparent 100%
        );
        transition: left 0.6s ease;
    }

    .vt-plus-glass:hover::before {
        left: 100%;
    }

    .vt-plus-glass:hover {
        border-color: rgba(217, 154, 78, 0.5);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.5),
            0 4px 12px rgba(217, 154, 78, 0.3),
            inset 0 1px 1px rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
    }

    /* Light theme hover adjustments */
    .light .vt-plus-glass:hover {
        border-color: rgba(217, 154, 78, 0.6);
        box-shadow:
            0 12px 40px rgba(217, 154, 78, 0.2),
            0 4px 12px rgba(217, 154, 78, 0.35),
            inset 0 1px 1px rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
    }

    /* Premium Glow Effects */
    .glow-blue {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }

    .glow-purple {
        box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
    }

    .glow-green {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }

    /* Enhanced Hover Effects */
    .hover-lift {
        transition:
            transform 0.2s ease,
            box-shadow 0.2s ease;
    }

    .hover-lift:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    /* Premium Gradient Backgrounds */
    .bg-premium-gradient {
        background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
    }

    .bg-brand-gradient {
        background: linear-gradient(135deg, hsl(var(--brand)) 0%, hsl(var(--primary)) 100%);
    }

    /* Smooth Reveal Animation */
    .reveal-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition:
            opacity 0.6s ease,
            transform 0.6s ease;
    }

    .reveal-on-scroll.revealed {
        opacity: 1;
        transform: translateY(0);
    }
}

mark.prompt-highlight {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    padding: 0.1rem 0.2rem;
    font-weight: 500;
    border: 1px solid hsl(var(--border));
    border-radius: 4px;
}

.dark mark.prompt-highlight {
    background-color: hsl(var(--brand) / 0.2);
    color: hsl(var(--brand));
    padding: 0.1rem 0.2rem;
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: 4px;
}

.tiptap .is-editor-empty:first-child::before {
    color: hsl(var(--muted-foreground) / 0.5);
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

.dark .tiptap .is-editor-empty:first-child::before {
    color: hsl(var(--muted-foreground));
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
}

.greeting-bubble::before {
    content: "";
    position: absolute;
    z-index: 0;
    bottom: 0;
    left: -7px;
    /* Tailwind doesn't support very specific pixel values like -7px */
    height: 20px;
    width: 20px;
    background: hsl(var(--muted));
    border-bottom-right-radius: 15px;
}

.greeting-bubble::after {
    content: "";
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: -10px;
    width: 10px;
    height: 20px;
    background: hsl(var(--background));
    border-bottom-right-radius: 10px;
}

@keyframes background-shine {
    from {
        background-position: 0 0;
    }
    to {
        background-position: -200% 0;
    }
}

@keyframes border-width {
    from {
        width: 10px;
        opacity: 0;
    }
    to {
        width: 100px;
        opacity: 1;
    }
}

.animate-background-shine {
    animation: background-shine 3s ease-in-out infinite;
}

.animate-border-width {
    animation: border-width 3s ease-in-out infinite;
}

/* Global text selection styles */
::selection {
    background-color: #bfb38f;
    color: #262626;
}

::-moz-selection {
    background-color: #bfb38f;
    color: #262626;
}
