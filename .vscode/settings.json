{"typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit", "source.fixAll.oxc": "explicit"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "oxc.enable": true, "markdown.validate.enabled": false, "cSpell.enabledFileTypes": {"markdown": false}, "npm.packageManager": "bun", "amp.experimental.enhancedRules": true, "typescript.autoClosingTags": false, "[toml]": {"editor.defaultFormatter": "tamasfe.even-better-toml"}}