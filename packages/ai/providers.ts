import { createAnthropic } from "@ai-sdk/anthropic";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { createGroq } from "@ai-sdk/groq";
import { createOpenAI } from "@ai-sdk/openai";
import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import type { LanguageModelV1 } from "@ai-sdk/provider";
import { createTogetherAI } from "@ai-sdk/togetherai";
import { createXai } from "@ai-sdk/xai";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { ChatMode } from "@repo/shared/config";
import { log } from "@repo/shared/logger";
import { type LanguageModelV1Middleware, wrapLanguageModel } from "ai";
export const Providers = {
    OPENAI: "openai",
    ANTHROPIC: "anthropic",
    TOGETHER: "together",
    GOOGLE: "google",
    FIREWORKS: "fireworks",
    XAI: "xai",
    OPENROUTER: "openrouter",
    LMSTUDIO: "lmstudio",
    OLLAMA: "ollama",
    GROQ: "groq",
    MOONSHOT: "moonshot",
} as const;

export type ProviderEnumType = (typeof Providers)[keyof typeof Providers];

import { CLAUDE_4_CONFIG } from "./constants/reasoning";
import { type ModelEnum, models } from "./models";
import { generateErrorMessage } from "./services/error-messages";

// Define a global type for API keys
declare global {
    interface Window {
        AI_API_KEYS?: {
            [key in ProviderEnumType]?: string;
        };
        JINA_API_KEY?: string;
        NEXT_PUBLIC_APP_URL?: string;
    }

    // Extend WorkerGlobalScope for web workers
    interface WorkerGlobalScope {
        AI_API_KEYS?: {
            [key in ProviderEnumType]?: string;
        };
    }
}

// Helper function to get API key from env, global, or BYOK keys
// Note: For LM Studio, this returns the base URL instead of an API key
const getApiKey = (
    provider: ProviderEnumType,
    byokKeys?: Record<string, string>,
    isVtPlus?: boolean,
): string => {
    // First check BYOK keys if provided
    if (byokKeys) {
        const keyMapping: Record<ProviderEnumType, string> = {
            [Providers.OPENAI]: "OPENAI_API_KEY",
            [Providers.ANTHROPIC]: "ANTHROPIC_API_KEY",
            [Providers.TOGETHER]: "TOGETHER_API_KEY",
            [Providers.GOOGLE]: "GEMINI_API_KEY",
            [Providers.FIREWORKS]: "FIREWORKS_API_KEY",
            [Providers.XAI]: "XAI_API_KEY",
            [Providers.OPENROUTER]: "OPENROUTER_API_KEY",
            [Providers.LMSTUDIO]: "LMSTUDIO_BASE_URL",
            [Providers.OLLAMA]: "OLLAMA_BASE_URL",
            [Providers.GROQ]: "GROQ_API_KEY",
            [Providers.MOONSHOT]: "MOONSHOT_API_KEY",
        };

        const byokKey = byokKeys[keyMapping[provider]];
        if (byokKey) return byokKey;
    }

    // Server-funded keys only for whitelisted providers (VT+ policy)
    if (typeof process !== "undefined" && process.env) {
        switch (provider) {
            case Providers.GOOGLE:
                // Only Gemini can use server-funded API key, and only for VT+ users
                if (isVtPlus) {
                    return process.env.GEMINI_API_KEY || "";
                }
                return "";
            case Providers.LMSTUDIO:
                return process.env.LMSTUDIO_BASE_URL || "";
            case Providers.OLLAMA:
                return process.env.OLLAMA_BASE_URL || "http://127.0.0.1:11434";
            case Providers.GROQ:
                return process.env.GROQ_API_KEY || "";
            case Providers.MOONSHOT:
                return process.env.MOONSHOT_API_KEY || "";
            default:
                // All other providers MUST use BYOK - no server-funded keys
                return "";
        }
    }

    // For worker environments (use self) - BYOK only
    if (typeof self !== "undefined") {
        // Check if AI_API_KEYS exists on self (worker environment)
        const workerSelf = self as unknown as WorkerGlobalScope;
        if (workerSelf.AI_API_KEYS?.[provider]) {
            return workerSelf.AI_API_KEYS[provider];
        }

        // For browser environments (self is also defined in browser)
        try {
            if (typeof window !== "undefined" && window.AI_API_KEYS) {
                return window.AI_API_KEYS[provider] || "";
            }
        } catch {
            // window is not available in this environment
        }
    }

    return "";
};

// Define proper return types for provider instances
type ProviderInstance =
    | ReturnType<typeof createOpenAI>
    | ReturnType<typeof createAnthropic>
    | ReturnType<typeof createGoogleGenerativeAI>
    | ReturnType<typeof createTogetherAI>
    | ReturnType<typeof createXai>
    | ReturnType<typeof createOpenRouter>
    | ReturnType<typeof createGroq>
    | ReturnType<typeof createOpenAICompatible>;

export const getProviderInstance = (
    provider: ProviderEnumType,
    byokKeys?: Record<string, string>,
    isFreeModel?: boolean,
    claude4InterleavedThinking?: boolean,
    isVtPlus?: boolean,
): ProviderInstance => {
    const apiKey = getApiKey(provider, byokKeys, isVtPlus);

    log.info("Provider instance debug:", {
        provider,
        isFreeModel,
        hasApiKey: !!apiKey,
        hasByokKeys: !!byokKeys,
        byokKeysKeys: byokKeys ? Object.keys(byokKeys) : undefined,
        apiKeyLength: apiKey ? apiKey.length : 0,
    });

    // Helper function to validate API key and throw consistent errors
    const validateApiKey = (providerType: ProviderEnumType): void => {
        if (!apiKey) {
            const errorMsg = generateErrorMessage("API key required", {
                provider: providerType,
                hasApiKey: false,
                isVtPlus,
            });
            throw new Error(errorMsg.message);
        }
    };

    // For free models, provide more helpful error messages if no API key is found
    if (isFreeModel && !apiKey && provider === "google") {
        log.error("No API key found for free Gemini model - checking environment...");
        log.error("Process env check:", {
            hasProcess: typeof process !== "undefined",
            hasEnv: typeof process !== "undefined" ? !!process.env : false,
            hasGeminiKey: typeof process !== "undefined" ? !!process.env?.GEMINI_API_KEY : false,
        });
    }

    switch (provider) {
        case Providers.OPENAI:
            validateApiKey(Providers.OPENAI);
            return createOpenAI({
                apiKey,
            });
        case "anthropic": {
            validateApiKey(Providers.ANTHROPIC);
            const headers: Record<string, string> = {
                "anthropic-dangerous-direct-browser-access": "true",
            };

            // Conditionally add Claude 4 interleaved thinking header
            if (claude4InterleavedThinking) {
                headers[CLAUDE_4_CONFIG.BETA_HEADER_KEY] = CLAUDE_4_CONFIG.BETA_HEADER;
            }

            return createAnthropic({
                apiKey,
                headers,
            });
        }
        case "together":
            validateApiKey(Providers.TOGETHER);
            return createTogetherAI({
                apiKey,
            });
        case "google":
            validateApiKey(Providers.GOOGLE);
            return createGoogleGenerativeAI({
                apiKey,
            });
        case "fireworks":
            validateApiKey(Providers.FIREWORKS);
            // Use OpenAI provider for Fireworks to avoid model ID transformation issues
            return createOpenAI({
                baseURL: "https://api.fireworks.ai/inference/v1",
                apiKey,
            });
        case "xai":
            validateApiKey(Providers.XAI);
            return createXai({
                apiKey,
            });
        case "openrouter":
            validateApiKey(Providers.OPENROUTER);
            return createOpenRouter({
                apiKey,
            });
        case "groq":
            validateApiKey(Providers.GROQ);
            return createGroq({
                apiKey,
            });
        case "moonshot":
            validateApiKey(Providers.MOONSHOT);
            return createOpenAI({
                baseURL: "https://api.moonshot.cn/v1",
                apiKey,
            });
        case "lmstudio": {
            // LM Studio uses baseURL instead of API key
            let rawURL = apiKey || "http://localhost:1234";

            // Add protocol if missing
            if (!/^https?:\/\//i.test(rawURL)) {
                rawURL = `http://${rawURL}`;
            }

            // For browser environments (not Web Workers or server-side), use proxy to avoid CORS/mixed content issues
            const isWebWorker = typeof importScripts === "function";
            const isServer = typeof process !== "undefined" && process.env;
            const isBrowser =
                typeof window !== "undefined" && window.location && !isWebWorker && !isServer;

            if (isBrowser) {
                // In production, use the proxy endpoint
                const isProduction = window.location.protocol === "https:";
                if (isProduction) {
                    return createOpenAI({
                        baseURL: "/api/lmstudio-proxy",
                        apiKey: "not-required",
                        headers: {
                            "x-lmstudio-url": rawURL,
                        },
                    });
                }
            }

            // Security: Validate that base URL is localhost only in development (prevent SSRF)
            const url = new URL(rawURL);
            const LOCAL_HOSTS = new Set(["localhost", "127.0.0.1", "::1", "0.0.0.0"]);
            const isProduction = process.env.NODE_ENV === "production";
            const allowRemote = process.env.ALLOW_REMOTE_LMSTUDIO === "true";

            if (!LOCAL_HOSTS.has(url.hostname) && !isProduction && !allowRemote) {
                const errorMsg = generateErrorMessage("Network connection error", {
                    provider: Providers.LMSTUDIO,
                    hasApiKey: true,
                    isVtPlus,
                    originalError: "Remote connections not allowed in development",
                });
                throw new Error(
                    `${errorMsg.message} Set ALLOW_REMOTE_LMSTUDIO=true to override or deploy to production. For help setting up LM Studio, see the guide at docs/guides/lm-studio-setup.md`,
                );
            }

            // Clean up URL and add /v1 endpoint if needed
            const baseURL = rawURL.includes("/v1")
                ? rawURL
                : `${url.origin.replace(/\/+$/, "")}/v1`;

            // Use the recommended @ai-sdk/openai-compatible for LM Studio
            return createOpenAICompatible({
                name: "lmstudio",
                baseURL,
            });
        }
        case "ollama": {
            // Ollama uses baseURL instead of API key and has OpenAI compatibility
            let rawURL = apiKey || "http://127.0.0.1:11434";

            // Add protocol if missing
            if (!/^https?:\/\//i.test(rawURL)) {
                rawURL = `http://${rawURL}`;
            }

            // For browser environments, use proxy to avoid CORS/mixed content issues
            if (typeof window !== "undefined" && window.location) {
                // In production, use the proxy endpoint
                const isProduction = window.location.protocol === "https:";
                if (isProduction) {
                    return createOpenAI({
                        baseURL: "/api/ollama-proxy",
                        apiKey: "not-required",
                        headers: {
                            "x-ollama-url": rawURL,
                        },
                    });
                }
            }

            // Security: Validate that base URL is localhost only in development (prevent SSRF)
            const url = new URL(rawURL);
            const LOCAL_HOSTS = new Set(["localhost", "127.0.0.1", "::1", "0.0.0.0"]);
            const isProduction = process.env.NODE_ENV === "production";
            const allowRemote = process.env.ALLOW_REMOTE_OLLAMA === "true";

            if (!LOCAL_HOSTS.has(url.hostname) && !isProduction && !allowRemote) {
                const errorMsg = generateErrorMessage("Network connection error", {
                    provider: Providers.OLLAMA,
                    hasApiKey: true,
                    isVtPlus,
                    originalError: "Remote connections not allowed in development",
                });
                throw new Error(
                    `${errorMsg.message} Set ALLOW_REMOTE_OLLAMA=true to override or deploy to production. For help setting up Ollama, see the guide at docs/guides/ollama-setup.md`,
                );
            }

            // Ollama uses /api/v1 endpoint for OpenAI compatibility
            const baseURL = rawURL.includes("/api/v1")
                ? rawURL
                : `${url.origin.replace(/\/+$/, "")}/api/v1`;

            // Use the recommended @ai-sdk/openai-compatible for Ollama
            return createOpenAICompatible({
                name: "ollama",
                baseURL,
            });
        }
        default:
            if (!apiKey) {
                const errorMsg = generateErrorMessage("API key required", {
                    provider: provider as ProviderEnumType,
                    hasApiKey: false,
                    isVtPlus,
                });
                throw new Error(errorMsg.message);
            }
            // Default to OpenAI-compatible for unknown providers
            return createOpenAI({
                apiKey,
            });
    }
};

export const getLanguageModel = (
    m: ModelEnum,
    middleware?: LanguageModelV1Middleware,
    byokKeys?: Record<string, string>,
    useSearchGrounding?: boolean,
    cachedContent?: string,
    claude4InterleavedThinking?: boolean,
    isVtPlus?: boolean,
) => {
    log.info("=== getLanguageModel START ===");
    log.info("Parameters:", {
        modelEnum: m,
        hasMiddleware: !!middleware,
        hasByokKeys: !!byokKeys,
        byokKeys: byokKeys ? Object.keys(byokKeys) : undefined,
        useSearchGrounding,
    });

    const model = models.find((model) => model.id === m);
    log.info("Found model:", {
        found: !!model,
        modelId: model?.id,
        modelName: model?.name,
        modelProvider: model?.provider,
    });

    if (!model) {
        log.error("Model not found:", { data: m });
        throw new Error(`Model ${m} not found`);
    }

    try {
        log.info("Getting provider instance for:", { data: model.provider });
        const instance = getProviderInstance(
            model?.provider as ProviderEnumType,
            byokKeys,
            model?.isFree,
            claude4InterleavedThinking,
            isVtPlus,
        );
        log.info("Provider instance created:", {
            hasInstance: !!instance,
            instanceType: typeof instance,
        });

        // Handle Gemini models with search grounding or caching
        if (model?.provider === "google" && (useSearchGrounding || cachedContent)) {
            log.info("Creating Gemini model with special options...");
            const modelId = model?.id || ChatMode.GEMINI_2_5_FLASH_LITE;
            log.info("Using model ID:", { data: modelId });

            try {
                const modelOptions: {
                    useSearchGrounding?: boolean;
                    cachedContent?: string;
                } = {};

                if (useSearchGrounding) {
                    modelOptions.useSearchGrounding = true;
                }

                if (cachedContent) {
                    modelOptions.cachedContent = cachedContent;
                }

                // Type assertion for model creation with options
                const createModel = instance as (
                    id: string,
                    options?: Record<string, unknown>,
                ) => LanguageModelV1;
                const selectedModel = createModel(modelId, modelOptions);
                log.info("Gemini model created with options:", {
                    hasModel: !!selectedModel,
                    modelType: typeof selectedModel,
                    useSearchGrounding,
                    hasCachedContent: !!cachedContent,
                });

                if (middleware) {
                    log.info("Wrapping model with middleware...");
                    return wrapLanguageModel({
                        model: selectedModel,
                        middleware,
                    }) as LanguageModelV1;
                }
                return selectedModel as LanguageModelV1;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                const errorStack = error instanceof Error ? error.stack : undefined;
                log.error("Error creating Gemini model with special options:", {
                    data: errorMessage,
                });
                if (errorStack) {
                    log.error("Error stack:", { data: errorStack });
                }
                throw error;
            }
        }

        log.info("Creating standard model...");
        const modelId = model?.id || ChatMode.GEMINI_2_5_FLASH_LITE;
        log.info("Using model ID:", { data: modelId });

        try {
            const createModel = instance as (id: string) => LanguageModelV1;
            const selectedModel = createModel(modelId);
            log.info("Standard model created:", {
                hasModel: !!selectedModel,
                modelType: typeof selectedModel,
            });

            if (middleware) {
                log.info("Wrapping model with middleware...");
                return wrapLanguageModel({
                    model: selectedModel,
                    middleware,
                }) as LanguageModelV1;
            }
            log.info("=== getLanguageModel END ===");
            return selectedModel as LanguageModelV1;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            const errorStack = error instanceof Error ? error.stack : undefined;
            log.error("Error creating standard model:", { data: errorMessage });
            if (errorStack) {
                log.error("Error stack:", { data: errorStack });
            }
            throw error;
        }
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorStack = error instanceof Error ? error.stack : undefined;
        log.error("Error in getLanguageModel:", { data: errorMessage });
        if (errorStack) {
            log.error("Error stack:", { data: errorStack });
        }

        // Re-throw the original error without modification to preserve
        // the clear, actionable error messages from getProviderInstance
        throw error;
    }
};
