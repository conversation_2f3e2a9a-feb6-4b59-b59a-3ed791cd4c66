# Production fly.toml - US-focused deployment
app = 'vtchat'
primary_region = 'sin'

# US-focused 2-region setup
# sin: Asia-Pacific (primary)
# iad: USA (Ashburn, Virginia) - Target market

[build]

[env]
# Disable detailed request logging
# Only errors/warnings, not requests
LOG_LEVEL = "warn"
NODE_ENV = 'production'
BASE_URL = 'https://vtchat.io.vn'
BETTER_AUTH_URL = 'https://vtchat.io.vn'
NEXT_PUBLIC_BASE_URL = 'https://vtchat.io.vn'
APP_URL = 'https://vtchat.io.vn'
NEXT_PUBLIC_APP_URL = 'https://vtchat.io.vn'
NEXT_PUBLIC_COMMON_URL = 'https://vtchat.io.vn'
NEXT_PUBLIC_BETTER_AUTH_URL = 'https://vtchat.io.vn'
CREEM_ENVIRONMENT = 'production'
BETTER_AUTH_ENV = 'production'

[http_service]
internal_port = 3000
force_https = true
auto_stop_machines = 'suspend'
auto_start_machines = true
min_machines_running = 0
processes = ['app']

[[http_service.checks]]
grace_period = "15s"
interval = "60s"
method = "GET"
timeout = "10s"
path = "/api/health"

[[http_service.checks]]
type = "tcp"
grace_period = "5s"
interval = "30s"
timeout = "2s"

[[vm]]
memory = '1gb'
cpu_kind = 'shared'
cpus = 1
